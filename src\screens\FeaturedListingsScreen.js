import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5,
  Entypo,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import TruckCard from "../components/TruckCard";
import { getFeaturedVehicles } from "../utils/api";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const regionOptions = [
  "All Regions",
  "Gauteng",
  "KwaZulu-Natal",
  "Western Cape",
  "Eastern Cape",
  "Limpopo",
  "Mpumalanga",
  "Free State",
  "North West",
  "Northern Cape",
];

const conditionOptions = ["All", "New", "Used"];

const categoryOptions = [
  {
    label: "Trucks",
    value: "trucks",
    icon: <Ionicons name="car" size={24} color="#fff" />,
  },
   {
    label: "Trailers",
    value: "trailers",
    icon: <FontAwesome5 name="bus" size={24} color="#fff" />,
  },
  {
    label: "Commercial",
    value: "commercial_vehicles",
    icon: <MaterialCommunityIcons name="truck" size={24} color="#fff" />,
  },
 
  {
    label: "Others",
    value: "others",
    icon: <Entypo name="dots-three-horizontal" size={24} color="#fff" />,
    description: "Farm equipment, construction, mining, agricultural equipment"
  },
];

const FeaturedListingsScreen = () => {
  const navigation = useNavigation();

  // Comprehensive unmount detection system
  const isMountedRef = useRef(true);
  const isUnmountingRef = useRef(false);
  const abortControllerRef = useRef(null);
  const timeoutsRef = useRef(new Set());
  const intervalsRef = useRef(new Set());
  const listenersRef = useRef(new Set());

  const [searchQuery, setSearchQuery] = useState("");
  const [featuredVehicles, setFeaturedVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [selectedCondition, setSelectedCondition] = useState("All");
  const [selectedRegion, setSelectedRegion] = useState("All Regions");
  const [minYear, setMinYear] = useState("");
  const [maxYear, setMaxYear] = useState("");
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState("trucks");
  const [lastRefreshTime, setLastRefreshTime] = useState(Date.now());

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalVehicles, setTotalVehicles] = useState(0);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const VEHICLES_PER_PAGE = 20;

  // Safe state update functions with mount checks
  const safeSetLoading = useCallback((value) => {
    if (isMountedRef.current && !isUnmountingRef.current) {
      setLoading(value);
    }
  }, []);

  const safeSetError = useCallback((value) => {
    if (isMountedRef.current && !isUnmountingRef.current) {
      setError(value);
    }
  }, []);

  const safeSetFeaturedVehicles = useCallback((value) => {
    if (isMountedRef.current && !isUnmountingRef.current) {
      setFeaturedVehicles(value);
    }
  }, []);

  const safeSetLastRefreshTime = useCallback((value) => {
    if (isMountedRef.current && !isUnmountingRef.current) {
      setLastRefreshTime(value);
    }
  }, []);

  const safeSetSearchQuery = useCallback((value) => {
    if (isMountedRef.current && !isUnmountingRef.current) {
      setSearchQuery(value);
    }
  }, []);

  // Cleanup function for all resources
  const cleanupResources = useCallback(() => {
    console.log('FeaturedListingsScreen: Starting cleanup...');

    // Mark as unmounting
    isUnmountingRef.current = true;
    isMountedRef.current = false;

    // Cancel any pending API requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Clear all timeouts
    timeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    timeoutsRef.current.clear();

    // Clear all intervals
    intervalsRef.current.forEach(intervalId => {
      clearInterval(intervalId);
    });
    intervalsRef.current.clear();

    // Remove all listeners
    listenersRef.current.forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    });
    listenersRef.current.clear();

    console.log('FeaturedListingsScreen: Cleanup completed');
  }, []);

  // Function to shuffle array randomly
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  const fetchFeaturedVehicles = useCallback(async (category = selectedCategory, page = 1, append = false) => {
    // Pre-flight checks
    if (!isMountedRef.current || isUnmountingRef.current) {
      console.log('FeaturedListingsScreen: Skipping fetch - component unmounted');
      return;
    }

    // Create new abort controller for this request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    try {
      console.log('FeaturedListingsScreen: Starting fetch for category:', category, 'page:', page);

      // Safe state update with mount check
      if (append) {
        setLoadingMore(true);
      } else {
        safeSetLoading(true);
        setCurrentPage(1);
        setHasMoreData(true);
      }
      safeSetError(null);

      // Double-check mount status before API call
      if (!isMountedRef.current || isUnmountingRef.current) {
        console.log('FeaturedListingsScreen: Aborting fetch - component unmounted during setup');
        return;
      }

      let response;

      if (category === 'others') {
        // For "others" category, we need to fetch from multiple categories since the backend
        // featured endpoint doesn't support exclude_categories properly
        const allCategories = ['heavy_machinery', 'animal_farming_equipment', 'farm_equipment', 'trailers'];
        const allVehicles = [];

        for (const cat of allCategories) {
          try {
            const catResponse = await getFeaturedVehicles(50, false, cat, {
              signal: abortControllerRef.current.signal,
              params: { page: 1, limit: 50 }
            });
            if (catResponse.vehicles) {
              allVehicles.push(...catResponse.vehicles);
            }
          } catch (err) {
            console.log(`Failed to fetch featured vehicles for category ${cat}:`, err.message);
          }
        }

        // Create a response object similar to the API response
        response = {
          vehicles: allVehicles,
          total: allVehicles.length
        };
      } else {
        // For specific categories, use the normal API call
        response = await getFeaturedVehicles(VEHICLES_PER_PAGE, false, category, {
          signal: abortControllerRef.current.signal,
          params: { page, limit: VEHICLES_PER_PAGE }
        });
      }

      // Triple-check mount status after API call
      if (!isMountedRef.current || isUnmountingRef.current) {
        console.log('FeaturedListingsScreen: Discarding response - component unmounted during fetch');
        return;
      }

      const newVehicles = response.vehicles || [];
      const total = response.total || newVehicles.length;

      // Update pagination state
      setTotalVehicles(total);
      setHasMoreData(newVehicles.length === VEHICLES_PER_PAGE && (page * VEHICLES_PER_PAGE) < total);
      setCurrentPage(page);

      // Final mount check before state updates
      if (isMountedRef.current && !isUnmountingRef.current) {
        if (append) {
          // Append new vehicles to existing list
          safeSetFeaturedVehicles(prev => [...prev, ...newVehicles]);
        } else {
          // Replace vehicles list (initial load or category change)
          const shuffledVehicles = shuffleArray(newVehicles);
          safeSetFeaturedVehicles(shuffledVehicles);
        }
        safeSetLastRefreshTime(Date.now());
        console.log('FeaturedListingsScreen: Successfully updated vehicles:', newVehicles.length);
      }
    } catch (err) {
      // Check if error is due to abort (component unmounted)
      if (err.name === 'AbortError') {
        console.log('FeaturedListingsScreen: Fetch aborted - component unmounted');
        return;
      }

      // Only handle error if component is still mounted
      if (isMountedRef.current && !isUnmountingRef.current) {
        console.error("FeaturedListingsScreen: Error fetching vehicles:", err);
        safeSetError("Failed to load featured vehicles. Please try again later.");
        if (!append) {
          safeSetFeaturedVehicles([]);
        }
      }
    } finally {
      // Safe loading state update
      if (isMountedRef.current && !isUnmountingRef.current) {
        safeSetLoading(false);
        setLoadingMore(false);
      }

      // Clean up abort controller
      if (abortControllerRef.current) {
        abortControllerRef.current = null;
      }
    }
  }, [selectedCategory, safeSetLoading, safeSetError, safeSetFeaturedVehicles, safeSetLastRefreshTime]);

  // Function to load more vehicles (pagination)
  const loadMoreVehicles = useCallback(async () => {
    if (!hasMoreData || loadingMore || loading || !isMountedRef.current || isUnmountingRef.current) return;

    const nextPage = currentPage + 1;
    await fetchFeaturedVehicles(selectedCategory, nextPage, true);
  }, [hasMoreData, loadingMore, loading, currentPage, selectedCategory, fetchFeaturedVehicles]);

  // Fetch vehicles when component mounts with comprehensive safety
  useEffect(() => {
   

    // Reset mount flags
    isMountedRef.current = true;
    isUnmountingRef.current = false;

    const fetchData = async () => {
      if (isMountedRef.current && !isUnmountingRef.current) {
        await fetchFeaturedVehicles();
      }
    };

    // Initial fetch with error handling
    fetchData().catch(err => {
      console.error('FeaturedListingsScreen: Initial fetch error:', err);
    });

    // Set up interval to refresh listings every 10 minutes with safety checks
    const refreshInterval = setInterval(() => {
      if (isMountedRef.current && !isUnmountingRef.current) {
        
        fetchFeaturedVehicles().catch(err => {
          console.error('FeaturedListingsScreen: Auto-refresh error:', err);
        });
      }
    }, 10 * 60 * 1000); // 10 minutes

    // Track interval for cleanup
    intervalsRef.current.add(refreshInterval);

    // Cleanup function
    return () => {
    
      isUnmountingRef.current = true;
      isMountedRef.current = false;

      // Clear this specific interval
      clearInterval(refreshInterval);
      intervalsRef.current.delete(refreshInterval);
    };
  }, [fetchFeaturedVehicles]);

  // Fetch vehicles when category changes with comprehensive safety
  useEffect(() => {
    // Skip if component is unmounting or unmounted
    if (isUnmountingRef.current || !isMountedRef.current) {
      return;
    }

    console.log('FeaturedListingsScreen: Category changed to:', selectedCategory);

    const fetchData = async () => {
      if (isMountedRef.current && !isUnmountingRef.current) {
        await fetchFeaturedVehicles(selectedCategory);
      }
    };

    // Fetch with error handling
    fetchData().catch(err => {
      if (isMountedRef.current && !isUnmountingRef.current) {
        console.error('FeaturedListingsScreen: Category change fetch error:', err);
      }
    });

    // No cleanup needed as fetchFeaturedVehicles handles its own abort
  }, [selectedCategory, fetchFeaturedVehicles]);

  // Handle navigation focus/blur events with comprehensive cleanup
  useEffect(() => {
    console.log('FeaturedListingsScreen: Setting up navigation listeners...');

    const unsubscribeFocus = navigation.addListener('focus', () => {
      console.log('FeaturedListingsScreen: Screen focused - resetting mount flags');
      isMountedRef.current = true;
      isUnmountingRef.current = false;
    });

    const unsubscribeBlur = navigation.addListener('blur', () => {
      console.log('FeaturedListingsScreen: Screen blurred - initiating cleanup');
      isUnmountingRef.current = true;

      // Set a timeout to mark as unmounted after blur
      const blurTimeout = setTimeout(() => {
        if (isUnmountingRef.current) {
          isMountedRef.current = false;
          console.log('FeaturedListingsScreen: Marked as unmounted after blur timeout');
        }
      }, 100);

      timeoutsRef.current.add(blurTimeout);
    });

    const unsubscribeBeforeRemove = navigation.addListener('beforeRemove', () => {
      console.log('FeaturedListingsScreen: Before remove - starting immediate cleanup');
      cleanupResources();
    });

    // Track listeners for cleanup
    listenersRef.current.add(unsubscribeFocus);
    listenersRef.current.add(unsubscribeBlur);
    listenersRef.current.add(unsubscribeBeforeRemove);

    return () => {
      console.log('FeaturedListingsScreen: Cleaning up navigation listeners...');
      unsubscribeFocus();
      unsubscribeBlur();
      unsubscribeBeforeRemove();

      // Remove from tracking
      listenersRef.current.delete(unsubscribeFocus);
      listenersRef.current.delete(unsubscribeBlur);
      listenersRef.current.delete(unsubscribeBeforeRemove);
    };
  }, [navigation, cleanupResources]);

  // Master cleanup effect - runs on unmount
  useEffect(() => {
    console.log('FeaturedListingsScreen: Master cleanup effect initialized');

    return () => {
      console.log('FeaturedListingsScreen: Master cleanup effect triggered - component unmounting');
      cleanupResources();
    };
  }, [cleanupResources]);

  // Additional safety net - cleanup on any error
  useEffect(() => {
    const handleError = (error) => {
      console.error('FeaturedListingsScreen: Unhandled error detected:', error);
      if (isMountedRef.current) {
        cleanupResources();
      }
    };

    // Add global error handler
    const originalHandler = global.ErrorUtils?.getGlobalHandler?.();
    if (global.ErrorUtils?.setGlobalHandler) {
      global.ErrorUtils.setGlobalHandler(handleError);
    }

    return () => {
      // Restore original error handler
      if (global.ErrorUtils?.setGlobalHandler && originalHandler) {
        global.ErrorUtils.setGlobalHandler(originalHandler);
      }
    };
  }, [cleanupResources]);

  const handleTruckPress = useCallback((item) => {
    // Pre-navigation safety checks
    if (!isMountedRef.current || isUnmountingRef.current) {
      console.log('FeaturedListingsScreen: Skipping navigation - component unmounted');
      return;
    }

    try {
      // Check if the vehicle is for hire
      const isHireVehicle = item.listing_type === 'hire' ||
                           item.for_hire === true ||
                           item.for_hire === 1;

      const targetScreen = isHireVehicle ? "HireDetails" : "TruckDetails";
      console.log(`FeaturedListingsScreen: Navigating to ${targetScreen} with item:`, item.vehicle_id || item.id,
                  `(isHire: ${isHireVehicle})`);

      // Additional safety check right before navigation
      if (isMountedRef.current && !isUnmountingRef.current) {
        navigation.navigate(targetScreen, { vehicle: item });
      } else {
        console.log('FeaturedListingsScreen: Navigation cancelled - component state changed');
      }
    } catch (error) {
      console.error('FeaturedListingsScreen: Error navigating to vehicle details:', error);

      // If navigation fails, ensure we don't leave the component in a bad state
      if (isMountedRef.current && !isUnmountingRef.current) {
        safeSetError('Navigation failed. Please try again.');
      }
    }
  }, [navigation, safeSetError]);

  const filterVehicles = (vehicles) => {
    return vehicles.filter((item) => {
      // Search query filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const searchableText = `${item.make} ${item.model} ${item.year} ${item.description || ""}`.toLowerCase();
        if (!searchableText.includes(query)) return false;
      }

      // Category filter - add client-side filtering for "Others" category as fallback
      if (selectedCategory !== "others") {
        if (item.category?.toLowerCase() !== selectedCategory.toLowerCase())
          return false;
      } else {
        // Others: show everything not in trucks, commercial_vehicles, and buses
        if (["trucks", "commercial_vehicles", "trailers"].includes(item.category?.toLowerCase()))
          return false;
      }

      // Condition filter
      if (
        selectedCondition !== "All" &&
        item.condition?.toLowerCase() !== selectedCondition.toLowerCase()
      )
        return false;

      // Region filter
      if (
        selectedRegion !== "All Regions" &&
        item.region?.toLowerCase() !== selectedRegion.toLowerCase()
      )
        return false;

      // Year filter
      if (minYear && item.year < parseInt(minYear)) return false;
      if (maxYear && item.year > parseInt(maxYear)) return false;

      // Price filter
      const price = parseFloat(item.price);
      if (minPrice && price < parseFloat(minPrice)) return false;
      if (maxPrice && price > parseFloat(maxPrice)) return false;

      return true;
    });
  };

  const filteredVehicles = filterVehicles(featuredVehicles);

  const handleDropdownPress = (title) => {
    setActiveDropdown(activeDropdown === title ? null : title);
  };

  const renderDropdown = (title, options, selectedValue, setValue) => {
    const isOpen = activeDropdown === title;

    return (
      <View
        style={[
          styles.dropdownContainer,
          isOpen && styles.dropdownContainerActive,
        ]}
      >
        <TouchableOpacity
          style={styles.dropdownButton}
          onPress={() => handleDropdownPress(title)}
        >
          <Text style={styles.dropdownButtonText}>{selectedValue}</Text>
          <Ionicons
            name={isOpen ? "chevron-up" : "chevron-down"}
            size={20}
            color="#666"
          />
        </TouchableOpacity>

        {isOpen && (
          <View style={styles.dropdownList}>
            {options.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.dropdownItem,
                  selectedValue === option && styles.dropdownItemSelected,
                ]}
                onPress={() => {
                  setValue(option);
                  setActiveDropdown(null);
                }}
              >
                <Text
                  style={[
                    styles.dropdownItemText,
                    selectedValue === option && styles.dropdownItemTextSelected,
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  const handlePriceChange = (text, type) => {
    const numericValue = text.replace(/[^0-9]/g, '');
    if (type === 'min') {
      setMinPrice(numericValue);
    } else {
      setMaxPrice(numericValue);
    }
  };

  const handleYearChange = (text, type) => {
    const numericValue = text.replace(/[^0-9]/g, '');
    if (type === 'min') {
      setMinYear(numericValue);
    } else {
      setMaxYear(numericValue);
    }
  };

  const clearFilters = useCallback(() => {
    // Safety check before clearing filters
    if (!isMountedRef.current || isUnmountingRef.current) {
      console.log('FeaturedListingsScreen: Skipping filter clear - component unmounted');
      return;
    }

    try {
      console.log('FeaturedListingsScreen: Clearing all filters');

      // Use safe state updates
      if (isMountedRef.current && !isUnmountingRef.current) {
        setSelectedCondition("All");
        setSelectedRegion("All Regions");
        setMinYear("");
        setMaxYear("");
        setMinPrice("");
        setMaxPrice("");
        safeSetSearchQuery("");
      }
    } catch (error) {
      console.error('FeaturedListingsScreen: Error clearing filters:', error);
    }
  }, [safeSetSearchQuery]);

  const renderVehicleItem = ({ item }) => {
    // Check if this is a hire vehicle
    const isHireVehicle = item.listing_type === 'hire' ||
                         item.for_hire === true ||
                         item.for_hire === 1;

    return (
      <View style={styles.cardWrapper}>
        <TruckCard
          item={item}
          onPress={() => handleTruckPress(item)}
          width={SCREEN_WIDTH * 0.42} // Reduced from 0.45
          style={[
            styles.card,
            isHireVehicle && styles.hireCard // Apply hire styling if it's a hire vehicle
          ]}
        />
      </View>
    );
  };

  // Final safety check before rendering
  if (isUnmountingRef.current) {
    console.log('FeaturedListingsScreen: Component is unmounting, rendering null');
    return null;
  }

  try {
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Featured Listings</Text>
            <Text style={styles.headerSubtitle}>
              {loading ? "Loading..." : `${filteredVehicles.length} vehicles`}
            </Text>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchBar}>
          <Ionicons
            name="search"
            size={20}
            color="#666"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Search featured vehicles..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {/* Category Tabs - Always visible */}
      <View style={styles.categoryTabsContainer}>
        {categoryOptions.map((cat) => {
          const isActive = selectedCategory === cat.value;
          return (
            <TouchableOpacity
              key={cat.value}
              style={[styles.categoryTab, isActive && styles.activeCategoryTab]}
              onPress={() => setSelectedCategory(cat.value)}
            >
              <View style={styles.categoryIconWrapper}>
                {React.cloneElement(cat.icon, {
                  color: isActive ? "#fff" : "#bb1010",
                })}
              </View>
              <Text
                style={[
                  styles.categoryTabText,
                  isActive && styles.activeCategoryTabText,
                ]}
              >
                {cat.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Filter Toggle Button - Always visible */}
      <TouchableOpacity
        style={styles.filterToggleButton}
        onPress={() => setShowFilters((prev) => !prev)}
      >
        <Ionicons name="filter" size={20} color="#fff" />
        <Text style={styles.filterToggleText}>
          Filters{showFilters ? " (Active)" : ""}
        </Text>
      </TouchableOpacity>

      {/* Filters Section - Always visible when toggled */}
      {showFilters && (
        <View style={styles.filtersSection}>
          {/* Region */}
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Region</Text>
            {renderDropdown(
              "Region",
              regionOptions,
              selectedRegion,
              setSelectedRegion
            )}
          </View>

          {/* Price Range */}
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Price Range</Text>
            <View style={styles.priceRangeContainer}>
              <TextInput
                style={styles.priceInput}
                placeholder="Min Price"
                placeholderTextColor="#666"
                value={minPrice}
                onChangeText={(text) => handlePriceChange(text, 'min')}
                keyboardType="numeric"
              />
              <Text style={styles.priceRangeSeparator}>-</Text>
              <TextInput
                style={styles.priceInput}
                placeholder="Max Price"
                placeholderTextColor="#666"
                value={maxPrice}
                onChangeText={(text) => handlePriceChange(text, 'max')}
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* Year Range */}
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Year Range</Text>
            <View style={styles.priceRangeContainer}>
              <TextInput
                style={styles.priceInput}
                placeholder="Min Year"
                placeholderTextColor="#666"
                value={minYear}
                onChangeText={(text) => handleYearChange(text, 'min')}
                keyboardType="numeric"
              />
              <Text style={styles.priceRangeSeparator}>-</Text>
              <TextInput
                style={styles.priceInput}
                placeholder="Max Year"
                placeholderTextColor="#666"
                value={maxYear}
                onChangeText={(text) => handleYearChange(text, 'max')}
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* Condition */}
          <View style={styles.filterRow}>
            <Text style={styles.filterLabel}>Condition</Text>
            {renderDropdown(
              "Condition",
              conditionOptions,
              selectedCondition,
              setSelectedCondition
            )}
          </View>

          {/* Clear Filters Button */}
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={clearFilters}
          >
            <Text style={styles.clearFiltersText}>Clear All Filters</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Main Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#bb1010" />
          <Text style={styles.loadingText}>Loading featured vehicles...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchFeaturedVehicles}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      ) : filteredVehicles.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="star-outline" size={48} color="#999" />
          <Text style={styles.emptyTitle}>No featured vehicles found</Text>
          <Text style={styles.emptySubtitle}>
            Try adjusting your filters or search criteria
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredVehicles}
          renderItem={renderVehicleItem}
          keyExtractor={(item) => `featured-${item.vehicle_id || item.id}`}
          numColumns={2}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={6}
          getItemLayout={null}
          removeClippedSubviews={false}
          onEndReached={loadMoreVehicles}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() => (
            loadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size="small" color="#bb1010" />
                <Text style={styles.loadingMoreText}>Loading more vehicles...</Text>
              </View>
            ) : hasMoreData ? (
              <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={loadMoreVehicles}
                disabled={loadingMore}
              >
                <Text style={styles.loadMoreButtonText}>Load More Vehicles</Text>
              </TouchableOpacity>
            ) : filteredVehicles.length > 0 ? (
              <View style={styles.endOfListContainer}>
                <Text style={styles.endOfListText}>
                  You've reached the end! {totalVehicles} vehicles total.
                </Text>
              </View>
            ) : null
          )}
        />
      )}
    </KeyboardAvoidingView>
  );
  } catch (renderError) {
    console.error('FeaturedListingsScreen: Render error caught:', renderError);

    // Cleanup on render error
    if (isMountedRef.current) {
      cleanupResources();
    }

    // Return safe fallback UI
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ fontSize: 18, color: '#333', textAlign: 'center', marginBottom: 16 }}>
          Something went wrong
        </Text>
        <Text style={{ fontSize: 14, color: '#666', textAlign: 'center', marginBottom: 24 }}>
          An error occurred while loading the featured listings. Please try navigating back and reopening this screen.
        </Text>
        <TouchableOpacity
          style={{ backgroundColor: '#bb1010', paddingHorizontal: 24, paddingVertical: 12, borderRadius: 8 }}
          onPress={() => {
            try {
              navigation.goBack();
            } catch (navError) {
              console.error('FeaturedListingsScreen: Navigation error in fallback:', navError);
            }
          }}
        >
          <Text style={{ color: '#fff', fontSize: 16, fontWeight: 'bold' }}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  contentContainer: {
    paddingBottom: 20,
  },
  headerContainer: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: Platform.OS === "ios" ? 50 : 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: "bold",
    color: "#333",
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 2,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  categoryTabsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    marginTop: 16,
    paddingHorizontal: 16,
  },
  categoryTab: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: "#fff",
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  activeCategoryTab: {
    backgroundColor: "#bb1010",
    borderColor: "#bb1010",
  },
  categoryTabText: {
    color: "#bb1010",
    fontWeight: "bold",
    fontSize: BASE_FONT_SIZE * 0.9,
    marginTop: 4,
  },
  activeCategoryTabText: {
    color: "#fff",
  },
  categoryIconWrapper: {
    alignItems: "center",
    justifyContent: "center",
  },
  filterToggleButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#bb1010",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginVertical: 16,
    marginHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
  },
  filterToggleText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "700",
    marginLeft: 10,
  },
  filtersSection: {
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    marginHorizontal: 16,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  filterRow: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: BASE_FONT_SIZE,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  dropdownContainer: {
    position: "relative",
    zIndex: 99,
  },
  dropdownContainerActive: {
    zIndex: 9999,
  },
  dropdownButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
  },
  dropdownButtonText: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  dropdownList: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    marginTop: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  dropdownItemSelected: {
    backgroundColor: "#f5f5f5",
  },
  dropdownItemText: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  dropdownItemTextSelected: {
    color: "#bb1010",
    fontWeight: "bold",
  },
  priceRangeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
  },
  priceInputContainer: {
    flex: 1,
  },
  priceInput: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
    padding: 8,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
  },
  priceRangeSeparator: {
    marginHorizontal: 8,
    fontSize: BASE_FONT_SIZE,
    color: "#666",
  },
  yearRangeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
  },
  yearInputContainer: {
    flex: 1,
  },
  yearInput: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
    padding: 8,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
    textAlign: "center",
  },
  yearRangeSeparator: {
    marginHorizontal: 8,
    fontSize: BASE_FONT_SIZE,
    color: "#666",
  },
  resetButton: {
    marginTop: 24,
    backgroundColor: "#bb1010",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  resetButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
    textAlign: "center",
  },
  resultsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    color: "#666",
    fontSize: BASE_FONT_SIZE,
  },
  errorContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  errorText: {
    fontSize: BASE_FONT_SIZE,
    color: "#FF6B6B",
    textAlign: "center",
    marginTop: 12,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: "#bb1010",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "bold",
    color: "#333",
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    textAlign: "center",
  },
  listContainer: {
    paddingBottom: 20,
  },
  row: {
    justifyContent: "space-between",
    paddingHorizontal: 4,
  },
  cardWrapper: {
    width: "48%",
    marginBottom: 16,
  },
  card: {
    width: "100%",
  },
  clearFiltersButton: {
    backgroundColor: "#FF6B6B",
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
    marginTop: 16,
  },
  clearFiltersText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "bold",
  },
  hireCard: {
    borderWidth: 2,
    borderColor: "#FF8C00",
    borderRadius: 12,
    overflow: "hidden",
  },
  // Pagination styles
  loadingMoreContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingMoreText: {
    marginTop: 8,
    color: '#666',
    fontSize: BASE_FONT_SIZE * 0.9,
  },
  loadMoreButton: {
    backgroundColor: '#bb1010',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    margin: 16,
    alignItems: 'center',
  },
  loadMoreButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
  },
  endOfListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  endOfListText: {
    color: '#666',
    fontSize: BASE_FONT_SIZE * 0.9,
    textAlign: 'center',
  },
});

export default FeaturedListingsScreen;
