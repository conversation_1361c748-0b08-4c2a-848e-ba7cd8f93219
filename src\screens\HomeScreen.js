import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  TextInput,
  Modal,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Linking,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5,
  Entypo,
} from "@expo/vector-icons";

import TruckCard from "../components/TruckCard";
import ChatWithSales from "../components/ChatWithSales";
import AdBanner from "../components/AdBanner";
import { HorizontalListingSkeleton } from "../components/ListingSkeleton";
import { getLatestVehicles, getFeaturedVehicles, getHireVehicles } from "../utils/api";
import { filterByPrice } from "../utils/priceFilter";
import {
  fullWidthBannerData,
  boxBannerData,
  bannerConfig,
  trackBannerClick
} from "../data/adBannerData";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const regionOptions = [
  "All Regions",
  "Gauteng",
  "KwaZulu-Natal",
  "Western Cape",
  "Eastern Cape",
  "Limpopo",
  "Mpumalanga",
  "Free State",
  "North West",
  "Northern Cape",
];
const yearOptions = [
  "All Years",
  "2024",
  "2023",
  "2022",
  "2021",
  "2020",
  "2019",
  "2018",
  "2017",
  "2016",
  "2015",
];
const priceOptions = [
  "Any Price",
  "Under R50,000",
  "R50,000 - R100,000",
  "R100,000 - R200,000",
  "R200,000 - R500,000",
  "Over R500,000",
];
const conditionOptions = ["All", "New", "Used"];

const categoryOptions = [
  {
    label: "Trucks",
    value: "trucks",
    icon: <Ionicons name="car" size={24} color="#fff" />,
  },
   {
    label: "Trailers",
    value: "trailers",
    icon: <FontAwesome5 name="bus" size={24} color="#fff" />,
  },
  {
    label: "Commercial",
    value: "commercial_vehicles",
    icon: <MaterialCommunityIcons name="truck" size={24} color="#fff" />,
  },
  {
    label: "Others",
    value: "others",
    icon: <Entypo name="dots-three-horizontal" size={24} color="#fff" />,
    description: "Farm equipment, construction, mining, agricultural equipment",
  },
];

const HomeScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [featuredVehicles, setFeaturedVehicles] = useState([]);
  const [latestVehicles, setLatestVehicles] = useState([]);
  const [hireVehicles, setHireVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [featuredLoading, setFeaturedLoading] = useState(true);
  const [latestLoading, setLatestLoading] = useState(true);
  const [hireLoading, setHireLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedPrice, setSelectedPrice] = useState("Any Price");
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [selectedCondition, setSelectedCondition] = useState("All");
  const [selectedRegion, setSelectedRegion] = useState("All Regions");
  const [selectedYear, setSelectedYear] = useState("All Years");
  const [minYear, setMinYear] = useState("");
  const [maxYear, setMaxYear] = useState("");
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState("trucks");

  // Randomization state for 15-minute refresh cycle
  const [randomizedFeatured, setRandomizedFeatured] = useState([]);
  const [randomizedLatest, setRandomizedLatest] = useState([]);
  const [randomizedHire, setRandomizedHire] = useState([]);
  const [lastRandomizeTime, setLastRandomizeTime] = useState(Date.now());
  const refreshIntervalRef = useRef(null);

  // Banner handling
  const handleBannerPress = (banner) => {
    console.log('Banner pressed:', banner);
    trackBannerClick(banner.id, banner.type, banner.advertiser);

    // Future: Handle banner URL opening or navigation
    if (banner.url) {
      // For now, just show an alert. In production, you might open the URL
      // Linking.openURL(banner.url);
    }
  };

  // Helper function to format time since last refresh
  const getTimeSinceRefresh = () => {
    const now = Date.now();
    const diffMinutes = Math.floor((now - lastRandomizeTime) / (1000 * 60));
    if (diffMinutes < 1) return "Just now";
    if (diffMinutes === 1) return "1 minute ago";
    return `${diffMinutes} minutes ago`;
  };

  // Utility function to shuffle array randomly
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Function to randomize and limit listings to 15
  const randomizeListings = (vehicles) => {
    const shuffled = shuffleArray(vehicles);
    return shuffled.slice(0, 15); // Maximum 15 listings
  };

  // Function to check if 15 minutes have passed and re-randomize if needed
  const checkAndUpdateRandomization = () => {
    const now = Date.now();
    const fifteenMinutes = 15 * 60 * 1000; // 15 minutes in milliseconds

    if (now - lastRandomizeTime >= fifteenMinutes) {
      console.log("15 minutes passed, re-randomizing listings...");
      setRandomizedFeatured(randomizeListings(featuredVehicles));
      setRandomizedLatest(randomizeListings(latestVehicles));
      setLastRandomizeTime(now);
    }
  };

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      setFeaturedLoading(true);
      setLatestLoading(true);

      // Fetch featured and latest vehicles separately to show individual loading states
      const featuredPromise = getFeaturedVehicles(20, false, selectedCategory)
        .then((response) => {
          const featured = response.vehicles || [];
          // Keep all featured vehicles (including hire vehicles if they're featured)
          setFeaturedVehicles(featured);
          setRandomizedFeatured(randomizeListings(featured));
          setFeaturedLoading(false);
          return featured;
        })
        .catch((err) => {
          console.error("Error fetching featured vehicles:", err);
          setFeaturedVehicles([]);
          setRandomizedFeatured([]);
          setFeaturedLoading(false);
          throw err;
        });

      const latestPromise = getLatestVehicles(20, false, selectedCategory)
        .then((response) => {
          const allLatest = response.vehicles || [];
          // Filter out hire vehicles from latest listings
          const latest = allLatest.filter(vehicle =>
            vehicle.listing_type !== 'hire' &&
            vehicle.for_hire !== true &&
            vehicle.for_hire !== 1
          );
          setLatestVehicles(latest);
          setRandomizedLatest(randomizeListings(latest));
          setLatestLoading(false);
          return latest;
        })
        .catch((err) => {
          console.error("Error fetching latest vehicles:", err);
          setLatestVehicles([]);
          setRandomizedLatest([]);
          setLatestLoading(false);
          throw err;
        });

      const hirePromise = getHireVehicles(15, selectedCategory === 'others' ? null : selectedCategory)
        .then((response) => {
          const allHire = response.vehicles || [];
          console.log("HomeScreen: Raw hire vehicles from API:", allHire.length);

          // Apply strict filtering to ensure only hire vehicles are shown
          let hire = allHire.filter(vehicle => {
            const isHire = vehicle.listing_type === 'hire' ||
                          vehicle.for_hire === true ||
                          vehicle.for_hire === 1;

            if (isHire) {
              console.log("HomeScreen: Valid hire vehicle:", {
                id: vehicle.vehicle_id,
                make: vehicle.make,
                model: vehicle.model,
                listing_type: vehicle.listing_type,
                for_hire: vehicle.for_hire,
                category: vehicle.category
              });
            }

            return isHire;
          });

          // Apply category filtering for hire vehicles (same logic as other sections)
          hire = hire.filter(vehicle => {
            if (selectedCategory === "trucks") {
              return vehicle.category?.toLowerCase() === "trucks";
            } else if (selectedCategory === "commercial_vehicles") {
              return vehicle.category?.toLowerCase() === "commercial_vehicles";
            } else if (selectedCategory === "trailers") {
              return vehicle.category?.toLowerCase() === "trailers";
            } else {
              // Others: show everything not in trucks, commercial_vehicles, and buses
              if (["trucks", "commercial_vehicles", "buses"].includes(vehicle.category?.toLowerCase()))
                return false;
            }
            return true;
          });


          setHireVehicles(hire);
          setRandomizedHire(randomizeListings(hire));
          setHireLoading(false);
          return hire;
        })
        .catch((err) => {
          console.error("Error fetching hire vehicles:", err);
          setHireVehicles([]);
          setRandomizedHire([]);
          setHireLoading(false);
          // Don't throw error for hire vehicles as it's not critical
          return [];
        });

      // Wait for all to complete
      await Promise.all([featuredPromise, latestPromise, hirePromise]);

      setLastRandomizeTime(Date.now());
      setError(null);
    } catch (err) {
      console.error("Error fetching vehicles:", err);
      setError("Failed to load vehicles. Please try again later.");
    } finally {
      setLoading(false);
      setFeaturedLoading(false);
      setLatestLoading(false);
      setHireLoading(false);
    }
  };

  useEffect(() => {
    fetchVehicles();
  }, []);

  // Refetch vehicles when category changes
  useEffect(() => {
    if (selectedCategory) {
      fetchVehicles();
    }
  }, [selectedCategory]);

  // Set up 15-minute refresh interval
  useEffect(() => {
    // Set up interval to check every minute if 15 minutes have passed
    // Also update the time display every minute
    refreshIntervalRef.current = setInterval(() => {
      checkAndUpdateRandomization();
      // Force re-render to update time display
      setLastRandomizeTime((prev) => prev); // Trigger re-render without changing the actual time
    }, 60000); // Check every minute

    // Cleanup interval on unmount
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [featuredVehicles, latestVehicles, lastRandomizeTime]);

  // Also check for randomization when app comes to foreground
  useEffect(() => {
    // Check immediately when component mounts or data changes
    if (featuredVehicles.length > 0 || latestVehicles.length > 0) {
      checkAndUpdateRandomization();
    }
  }, [featuredVehicles, latestVehicles]);

  const handleTruckPress = (item) => {
    // Check if the vehicle is for hire
    const isHireVehicle = item.listing_type === 'hire' ||
                         item.for_hire === true ||
                         item.for_hire === 1;

    if (isHireVehicle) {
      // Navigate to HireDetails for hire vehicles
      navigation.navigate("HireDetails", { vehicle: item });
    } else {
      // Navigate to TruckDetails for sale vehicles
      navigation.navigate("TruckDetails", { vehicle: item });
    }
  };

  const filterVehicles = (vehicles) => {
    return vehicles.filter((item) => {
      // Category filter
      if (selectedCategory !== "others") {
        if (item.category?.toLowerCase() !== selectedCategory.toLowerCase())
          return false;
      } else {
        // Others: show everything not in trucks, commercial, and buses (include machinery, spares, trailers, etc.)
        if (["trucks", "commercial_vehicles", "trailers"].includes(item.category?.toLowerCase()))
          return false;
      }
      // Condition filter
      if (
        selectedCondition !== "All" &&
        item.condition_type?.toLowerCase() !== selectedCondition.toLowerCase()
      )
        return false;
      // Region filter
      if (
        selectedRegion !== "All Regions" &&
        item.region?.toLowerCase() !== selectedRegion.toLowerCase()
      )
        return false;
      // Year filter
      if (minYear && item.year < parseInt(minYear)) return false;
      if (maxYear && item.year > parseInt(maxYear)) return false;
      // Price filter
      const price = parseFloat(item.price);
      if (minPrice && price < parseFloat(minPrice)) return false;
      if (maxPrice && price > parseFloat(maxPrice)) return false;
      return true;
    });
  };

  // Use randomized arrays for display, but apply filters to them
  const filteredLatest = filterVehicles(randomizedLatest);
  const filteredFeatured = filterVehicles(randomizedFeatured);
  const totalFiltered = filteredLatest.length;

  const handleDropdownPress = (title) => {
    setActiveDropdown(activeDropdown === title ? null : title);
  };

  const renderDropdown = (title, options, selectedValue, setValue) => {
    const isOpen = activeDropdown === title;

    return (
      <View
        style={[
          styles.dropdownContainer,
          isOpen && styles.dropdownContainerActive,
        ]}
      >
        <TouchableOpacity
          style={styles.dropdownButton}
          onPress={() => handleDropdownPress(title)}
        >
          <Text style={styles.dropdownButtonText}>{selectedValue}</Text>
          <Ionicons
            name={isOpen ? "chevron-up" : "chevron-down"}
            size={20}
            color="#666"
          />
        </TouchableOpacity>

        {isOpen && (
          <View
            style={[
              styles.dropdownList,
              title === "Year" && styles.dropdownListYear,
              title === "Region" && styles.dropdownListRegion,
            ]}
          >
            {options.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.dropdownItem,
                  selectedValue === option && styles.dropdownItemSelected,
                ]}
                onPress={() => {
                  setValue(option);
                  setActiveDropdown(null);
                }}
              >
                <Text
                  style={[
                    styles.dropdownItemText,
                    selectedValue === option && styles.dropdownItemTextSelected,
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  const handlePriceChange = (text, type) => {
    const numericValue = text.replace(/[^0-9]/g, "");
    if (type === "min") {
      setMinPrice(numericValue);
    } else {
      setMaxPrice(numericValue);
    }
  };

  const handleYearChange = (text, type) => {
    const numericValue = text.replace(/[^0-9]/g, "");
    if (type === "min") {
      setMinYear(numericValue);
    } else {
      setMaxYear(numericValue);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      <ScrollView
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="none"
        contentContainerStyle={styles.contentContainer}
      >
        {/* Category Tabs */}
        <View style={styles.categoryTabsContainer}>
          {categoryOptions.map((cat) => {
            const isActive = selectedCategory === cat.value;
            return (
              <TouchableOpacity
                key={cat.value}
                style={[
                  styles.categoryTab,
                  isActive && styles.activeCategoryTab,
                ]}
                onPress={() => setSelectedCategory(cat.value)}
              >
                <View style={styles.categoryIconWrapper}>
                  {React.cloneElement(cat.icon, {
                    color: isActive ? "#fff" : "#bb1010",
                  })}
                </View>
                <Text
                  style={[
                    styles.categoryTabText,
                    isActive && styles.activeCategoryTabText,
                  ]}
                >
                  {cat.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Full-Width Advertisement Banner */}
        <AdBanner
          bannerType="full-width"
          bannerData={fullWidthBannerData}
          rotationInterval={bannerConfig.fullWidth.rotationInterval}
          onBannerPress={handleBannerPress}
          style={styles.fullWidthBannerContainer}
        />

        {/* Action Buttons Row */}
        <View style={styles.actionButtonsContainer}>
          {/* Search Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={() => setShowFilters(false)}
          >
            <Ionicons name="search" size={18} color="#fff" />
            <Text style={styles.primaryButtonText}>Search</Text>
          </TouchableOpacity>

          {/* Filter Toggle Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => setShowFilters((prev) => !prev)}
          >
            <Ionicons name="filter" size={18} color="#bb1010" />
            <Text style={styles.secondaryButtonText}>
              Filters{showFilters ? " ✓" : ""}
            </Text>
          </TouchableOpacity>

          {/* Advanced Filters Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => navigation.navigate('AdvancedSearch')}
          >
            <Ionicons name="options" size={18} color="#bb1010" />
            <Text style={styles.secondaryButtonText}>Advanced</Text>
          </TouchableOpacity>
        </View>

        {/* Filters Section */}
        {showFilters && (
          <View style={styles.filtersSection}>
            {/* Region */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Region</Text>
              {renderDropdown(
                "Region",
                regionOptions,
                selectedRegion,
                setSelectedRegion
              )}
            </View>

            {/* Price Range */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Price Range (R)</Text>
              <View style={styles.priceRangeContainer}>
                <View style={styles.priceInputContainer}>
                  <TextInput
                    style={styles.priceInput}
                    placeholder="Min Price"
                    keyboardType="numeric"
                    value={minPrice}
                    onChangeText={(text) => handlePriceChange(text, "min")}
                  />
                </View>
                <Text style={styles.priceRangeSeparator}>to</Text>
                <View style={styles.priceInputContainer}>
                  <TextInput
                    style={styles.priceInput}
                    placeholder="Max Price"
                    keyboardType="numeric"
                    value={maxPrice}
                    onChangeText={(text) => handlePriceChange(text, "max")}
                  />
                </View>
              </View>
            </View>

            {/* Condition (New/Used) */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Condition</Text>
              {renderDropdown(
                "Condition",
                conditionOptions,
                selectedCondition,
                setSelectedCondition
              )}
            </View>

            {/* Year Range */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Year Range</Text>
              <View style={styles.yearRangeContainer}>
                <View style={styles.yearInputContainer}>
                  <TextInput
                    style={styles.yearInput}
                    placeholder="Min Year"
                    keyboardType="numeric"
                    maxLength={4}
                    value={minYear}
                    onChangeText={(text) => handleYearChange(text, "min")}
                  />
                </View>
                <Text style={styles.yearRangeSeparator}>to</Text>
                <View style={styles.yearInputContainer}>
                  <TextInput
                    style={styles.yearInput}
                    placeholder="Max Year"
                    keyboardType="numeric"
                    maxLength={4}
                    value={maxYear}
                    onChangeText={(text) => handleYearChange(text, "max")}
                  />
                </View>
              </View>
            </View>

            {/* Reset Filters Button */}
            <TouchableOpacity
              style={styles.resetButton}
              onPress={() => {
                setSelectedPrice("Any Price");
                setSelectedCondition("All");
                setSelectedRegion("All Regions");
                setMinYear("");
                setMaxYear("");
                setMinPrice("");
                setMaxPrice("");
              }}
            >
              <Text style={styles.resetButtonText}>Reset Filters</Text>
            </TouchableOpacity>
          </View>
        )}



        {/* Featured Listings */}
        <View style={styles.listingsHeader}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Featured Listings</Text>
            <Text style={styles.listingCount}>
              {featuredLoading
                ? "(Loading...)"
                : `(${filteredFeatured.length} of ${featuredVehicles.length})`}
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={() => {
                if (!featuredLoading && !latestLoading) {
                  setRandomizedFeatured(randomizeListings(featuredVehicles));
                  setRandomizedLatest(randomizeListings(latestVehicles));
                  setLastRandomizeTime(Date.now());
                }
              }}
              disabled={featuredLoading || latestLoading}
            >
              <Ionicons
                name="refresh"
                size={16}
                color={featuredLoading || latestLoading ? "#ccc" : "#bb1010"}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => navigation.navigate("FeaturedListings")}
            >
              <Text style={styles.seeAll}>See all</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Refresh Status */}
        {/* <View style={styles.refreshStatus}>
          <Text style={styles.refreshStatusText}>
            {featuredLoading || latestLoading
              ? "Loading listings..."
              : `Refreshed ${getTimeSinceRefresh()} • Auto-refresh every 15 minutes`}
          </Text>
        </View> */}
        <View style={styles.listingsContainer}>
          {featuredLoading ? (
            <HorizontalListingSkeleton count={3} />
          ) : filteredFeatured.length > 0 ? (
            <FlatList
              horizontal
              data={filteredFeatured}
              keyExtractor={(item) => `featured-${item.vehicle_id || item.id}`}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.featuredListContainer}
              renderItem={({ item }) => {
                // Check if this is a hire vehicle
                const isHireVehicle = item.listing_type === 'hire' ||
                                     item.for_hire === true ||
                                     item.for_hire === 1;

                return (
                  <View style={styles.featuredCardWrapper}>
                    <TruckCard
                      item={item}
                      onPress={() => handleTruckPress(item)}
                      width={SCREEN_WIDTH * 0.65} // Reduced from 0.75
                      style={[
                        styles.featuredCard,
                        isHireVehicle && styles.hireCard // Apply hire styling if it's a hire vehicle
                      ]}
                    />
                  </View>
                );
              }}
            />
          ) : (
            <View style={styles.emptyListingContainer}>
              <Ionicons name="star-outline" size={48} color="#ccc" />
              <Text style={styles.emptyListingText}>
                No featured listings available
              </Text>
              <Text style={styles.emptyListingSubText}>
                Check back later for new featured vehicles
              </Text>
            </View>
          )}
        </View>

        {/* For Hire Listings */}
        <View style={styles.listingsHeader}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>For Hire</Text>
            <Text style={styles.listingCount}>
              {hireLoading
                ? "(Loading...)"
                : `(${randomizedHire.length} available)`}
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.seeAllButton}
              onPress={() => navigation.navigate("Hire")}
            >
              <Text style={styles.seeAll}>See all</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.listingsContainer}>
          {hireLoading ? (
            <HorizontalListingSkeleton count={3} />
          ) : randomizedHire.length > 0 ? (
            <FlatList
              data={randomizedHire}
              renderItem={({ item }) => (
                <TruckCard
                  item={item}
                  onPress={() => navigation.navigate("HireDetails", { vehicle: item })}
                  width={SCREEN_WIDTH * 0.65} // Reduced from 0.75
                  style={styles.hireCard}
                />
              )}
              keyExtractor={(item) => `hire-${item.vehicle_id || item.id}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.hireListContainer}
              ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
            />
          ) : (
            <View style={styles.emptyListingContainer}>
              <Ionicons name="calendar-outline" size={48} color="#ccc" />
              <Text style={styles.emptyListingText}>
                No vehicles available for hire
              </Text>
              <Text style={styles.emptyListingSubText}>
                {hireVehicles.length === 0
                  ? "No hire vehicles found in the database"
                  : "Check back later for new hire opportunities"
                }
              </Text>
              {__DEV__ && (
                <Text style={styles.debugText}>
                  Debug: {hireVehicles.length} hire vehicles loaded
                </Text>
              )}
            </View>
          )}
        </View>

        {/* Box Advertisement Banner */}
        <AdBanner
          bannerType="box"
          bannerData={boxBannerData}
          rotationInterval={bannerConfig.box.rotationInterval}
          onBannerPress={handleBannerPress}
          style={styles.boxBannerContainer}
        />

        {/* Latest Listings Header */}
        <View style={[styles.listingsHeader, styles.latestListingsHeader]}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Latest Listings</Text>
            <Text style={styles.listingCount}>
              {latestLoading
                ? "(Loading...)"
                : `(${filteredLatest.length} of ${latestVehicles.length})`}
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              onPress={() => navigation.navigate("LatestListings")}
            >
              <Text style={styles.seeAll}>See all</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Latest Listings */}
        <View style={styles.listingsContainer}>
          {latestLoading ? (
            <HorizontalListingSkeleton count={3} />
          ) : filteredLatest.length > 0 ? (
            <FlatList
              horizontal
              data={filteredLatest}
              keyExtractor={(item) => `latest-${item.vehicle_id || item.id}`}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.latestListContainer}
              renderItem={({ item }) => (
                <View style={styles.latestCardWrapper}>
                  <TruckCard
                    item={item}
                    onPress={() => handleTruckPress(item)}
                    width={SCREEN_WIDTH * 0.65} // Reduced from 0.75
                    style={styles.latestCard}
                  />
                </View>
              )}
            />
          ) : (
            <View style={styles.noResultsContainer}>
              <Ionicons name="alert-circle-outline" size={48} color="#666" />
              <Text style={styles.noResultsText}>
                No matching listings found
              </Text>
              <Text style={styles.noResultsSubText}>
                Try adjusting your filters or search criteria
              </Text>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={fetchVehicles}
              >
                <Text style={styles.resetButtonText}>Refresh</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Bottom Action Buttons */}
        <View style={styles.bottomButtonsContainer}>
          <Text style={styles.bottomButtonsTitle}>Quick Actions</Text>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.bottomButton}
              onPress={() => navigation.navigate('DealershipsScreen')}
            >
              <Ionicons name="business" size={20} color="#bb1010" />
              <Text style={styles.bottomButtonText}>See All Dealerships</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.bottomButton}
              onPress={() => navigation.navigate('RegisterDealerScreen')}
            >
              <Ionicons name="person-add" size={20} color="#bb1010" />
              <Text style={styles.bottomButtonText}>Register as Dealer</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.bottomButton}
              onPress={() => Linking.openURL('tel:+***********')}
            >
              <Ionicons name="call" size={20} color="#bb1010" />
              <Text style={styles.bottomButtonText}>Call Trucks on Sale</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.bottomButton}
              onPress={() => Linking.openURL('https://wa.me/+***********')}
            >
              <Ionicons name="logo-whatsapp" size={20} color="#25D366" />
              <Text style={styles.bottomButtonText}>WhatsApp Us</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.bottomButton}
              onPress={() => navigation.navigate('PrivacyPolicyScreen')}
            >
              <Ionicons name="shield-checkmark" size={20} color="#bb1010" />
              <Text style={styles.bottomButtonText}>Privacy Policy</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.bottomButton}
              onPress={() => Linking.openURL('https://trucksonsale.co.za/listing.php?action=home')}
            >
              <Ionicons name="globe" size={20} color="#bb1010" />
              <Text style={styles.bottomButtonText}>Admin Portal</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  contentContainer: {
    padding: 12,
  },
  searchContainer: {
    marginBottom: 12, // Reduced from 16
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  categoryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12, // Reduced from 16
  },
  categoryButton: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
    flex: 1,
  },
  activeCategoryButton: {
    backgroundColor: "#bb1010",
    borderRadius: 4,
  },
  categoryButtonText: {
    fontSize: BASE_FONT_SIZE * 0.7,
    fontWeight: "600",
    color: "#333",
    marginTop: 4,
  },
  activeCategoryButtonText: {
    color: "#fff",
  },
  priceRangeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
  },
  priceInputContainer: {
    flex: 1,
  },
  priceInput: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
    padding: 8,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
  },
  priceRangeSeparator: {
    marginHorizontal: 8,
    fontSize: BASE_FONT_SIZE,
    color: "#666",
  },
  tabContainer: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 8,
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    marginRight: 8,
    marginBottom: 8,
  },
  activeTabButton: {
    backgroundColor: "#bb1010",
  },
  tabButtonText: {
    color: "#333",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
  },
  activeTabButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },

  listingsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.0,
    fontWeight: "light",
    color: "#333",
  },
  seeAll: {
    color: "#bb1010",
    fontSize: BASE_FONT_SIZE * 0.8,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
  card: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    width: "100%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  image: {
    width: "100%",
    height: SCREEN_WIDTH > 768 ? 160 : 180, // Reduced from 180:200
    aspectRatio: 16 / 9,
  },
  detailsContainer: {
    padding: 12, // Reduced from 16
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  title: {
    fontSize: BASE_FONT_SIZE * 1.125,
    fontWeight: "bold",
    color: "#333",
    flex: 1,
  },
  model: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 4,
  },
  price: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "bold",
    color: "#bb1010",
    marginTop: 8,
  },
  badgeContainer: {
    flexDirection: "row",
    marginTop: 12,
    gap: 8,
  },
  badge: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  badgeText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
  },
  shareButton: {
    padding: 8,
  },

  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 16,
    paddingHorizontal: 4,
    gap: 8,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 12,
    minHeight: 44, // Accessibility minimum touch target
    flex: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  primaryButton: {
    backgroundColor: "#bb1010",
    flex: 1.2, // Slightly larger for primary action
  },
  secondaryButton: {
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#bb1010",
    flex: 1,
  },
  primaryButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE * 0.9,
    fontWeight: "bold",
    marginLeft: 6,
  },
  secondaryButtonText: {
    color: "#bb1010",
    fontSize: BASE_FONT_SIZE * 0.85,
    fontWeight: "600",
    marginLeft: 4,
  },
  filtersSection: {
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  featuredCardWrapper: {
    marginRight: 12,
  },
  featuredCard: {
    marginBottom: 0,
  },
  card: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  image: {
    width: "100%",
    height: SCREEN_WIDTH > 768 ? 160 : 180, // Reduced from 180:200
    aspectRatio: 16 / 9,
  },
  detailsContainer: {
    padding: 12, // Reduced from 16
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  title: {
    fontSize: BASE_FONT_SIZE * 1.125,
    fontWeight: "bold",
    color: "#333",
    flex: 1,
  },
  model: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 4,
  },
  price: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: "bold",
    color: "#bb1010",
    marginTop: 8,
  },
  badgeContainer: {
    flexDirection: "row",
    marginTop: 12,
    gap: 8,
  },
  badge: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  badgeText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
  },
  shareButton: {
    padding: 8,
  },


  filtersSection: {
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  listingsContainer: {
    marginBottom: 24,
  },
  featuredListContainer: {
    paddingHorizontal: 5,
    paddingBottom: 24,
  },
  latestListContainer: {
    paddingHorizontal: 5,
    paddingBottom: 24,
  },
  latestCardWrapper: {
    marginRight: 10,
    marginBottom: 16,
  },
  latestCard: {
    marginBottom: 0,
  },
  latestListingsHeader: {
    marginTop: 24,
    marginBottom: 16,
  },
  latestListingsHeader: {
    marginTop: 8,
  },

  noResultsContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    margin: 16,
  },
  noResultsText: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: "bold",
    color: "#333",
    marginTop: 16,
  },
  noResultsSubText: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
  resetButton: {
    marginTop: 24,
    backgroundColor: "#bb1010",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  resetButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
  },
  filterRow: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: BASE_FONT_SIZE,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  dropdownContainer: {
    position: "relative",
    zIndex: 99,
  },
  dropdownContainerActive: {
    zIndex: 9999,
  },
  dropdownButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
  },
  dropdownButtonText: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  dropdownList: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    marginTop: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownListYear: {
    zIndex: 9999,
  },
  dropdownListRegion: {
    zIndex: 9999,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  dropdownItemSelected: {
    backgroundColor: "#f5f5f5",
  },
  dropdownItemText: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  dropdownItemTextSelected: {
    color: "#bb1010",
    fontWeight: "bold",
  },
  categoryTabsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    marginTop: 16,
    paddingHorizontal: 1,
  },
  categoryTab: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: "#fff",
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  activeCategoryTab: {
    backgroundColor: "#bb1010",
    borderColor: "#bb1010",
  },
  categoryTabText: {
    color: "#bb1010",
    fontWeight: "bold",
    fontSize: BASE_FONT_SIZE * 0.9,
    marginTop: 4,
  },
  activeCategoryTabText: {
    color: "#fff",
  },
  categoryIconWrapper: {
    alignItems: "center",
    justifyContent: "center",
  },
  yearRangeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
  },
  yearInputContainer: {
    flex: 1,
  },
  yearInput: {
    fontSize: BASE_FONT_SIZE,
    color: "#333",
    padding: 8,
    backgroundColor: "#f5f5f5",
    borderRadius: 4,
  },
  yearRangeSeparator: {
    marginHorizontal: 8,
    fontSize: BASE_FONT_SIZE,
    color: "#666",
  },
  headerTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  listingCount: {
    fontSize: 12,
    color: "#666",
    marginLeft: 8,
    fontWeight: "500",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  refreshButton: {
    padding: 8,
    marginRight: 8,
    borderRadius: 4,
    backgroundColor: "#f5f5f5",
  },
  refreshStatus: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: "#f8f9fa",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  refreshStatusText: {
    fontSize: 12,
    color: "#6c757d",
    textAlign: "center",
    fontStyle: "italic",
  },
  debugButton: {
    backgroundColor: "#bb1010",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginTop: 8,
    alignSelf: "flex-end",
  },
  debugButtonText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  emptyListingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    margin: 16,
  },
  emptyListingText: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "bold",
    color: "#333",
    marginTop: 12,
    marginBottom: 4,
  },
  emptyListingSubText: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: "#666",
    textAlign: "center",
  },
  hireCard: {
    borderWidth: 2,
    borderColor: "#FF8C00",
    borderRadius: 12,
    overflow: "hidden",
  },
  hireListContainer: {
    paddingHorizontal: 5,
    paddingBottom: 24,
  },
  debugText: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#999",
    marginTop: 8,
    fontStyle: "italic",
  },
  // Bottom buttons styles
  bottomButtonsContainer: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 20,
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  bottomButtonsTitle: {
    fontSize: BASE_FONT_SIZE * 1.2,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  bottomButton: {
    flex: 1,
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  bottomButtonText: {
    fontSize: BASE_FONT_SIZE * 0.85,
    color: '#333',
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },
  // Advertisement Banner Styles
  fullWidthBannerContainer: {
    marginVertical: 8,
    marginHorizontal: 0,
  },
  boxBannerContainer: {
    marginVertical: 16,
    marginHorizontal: 16,
    alignSelf: 'center',
  },
});

export default HomeScreen;
