import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  Dimensions, 
  useWindowDimensions, 
  Animated, 
  ActivityIndicator, 
  Alert, 
  Share 
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import EnquiryModal from '../components/EnquiryModal';
import { getVehicleById, checkVehicleAvailability, createHireBooking } from '../utils/api';
import ImageCarousel from '../components/ImageCarousel';

const HireDetailsScreen = ({ route, navigation }) => {
  const { width } = useWindowDimensions();
  const { vehicle } = route.params || {};
  const [truck, setTruck] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEnquiryModalVisible, setIsEnquiryModalVisible] = useState(false);
  const [selectedRentalPeriod, setSelectedRentalPeriod] = useState('daily');
  const [availability, setAvailability] = useState(null);
  const [checkingAvailability, setCheckingAvailability] = useState(false);

  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];

  // Helper function to get default rates for a vehicle
  const getDefaultRateForVehicle = (vehicle) => {
    const category = vehicle?.category?.toLowerCase() || '';
    const make = vehicle?.make?.toLowerCase() || '';

    // Base rates by vehicle category
    if (category.includes('truck') || category.includes('heavy')) {
      return { daily: 1500, weekly: 9000, monthly: 30000 };
    } else if (category.includes('bus')) {
      return { daily: 2000, weekly: 12000, monthly: 40000 };
    } else if (category.includes('commercial')) {
      return { daily: 800, weekly: 4800, monthly: 16000 };
    } else if (category.includes('farm') || category.includes('equipment')) {
      return { daily: 1000, weekly: 6000, monthly: 20000 };
    }

    // Default rates
    return { daily: 1200, weekly: 7500, monthly: 25000 };
  };

  // Dynamic rental pricing structure based on database values
  const getRentalRates = () => {
    if (!truck) {
      console.log('No truck data available for rental rates');
      return {};
    }

    console.log('Calculating rental rates from truck data:', {
      daily_rate: truck.daily_rate,
      weekly_rate: truck.weekly_rate,
      monthly_rate: truck.monthly_rate
    });

    // Parse rates as numbers and handle null/undefined values
    const dailyRate = parseFloat(truck.daily_rate) || 0;
    const weeklyRate = parseFloat(truck.weekly_rate) || 0;
    const monthlyRate = parseFloat(truck.monthly_rate) || 0;

    // Calculate fallback rates based on vehicle type and daily rate or use intelligent defaults
    const getDefaultRateByCategory = () => {
      const category = truck.category?.toLowerCase() || '';
      const make = truck.make?.toLowerCase() || '';

      // Base rates by vehicle category
      if (category.includes('truck') || category.includes('heavy')) {
        return { daily: 1500, weekly: 9000, monthly: 30000 };
      } else if (category.includes('bus')) {
        return { daily: 2000, weekly: 12000, monthly: 40000 };
      } else if (category.includes('commercial')) {
        return { daily: 800, weekly: 4800, monthly: 16000 };
      } else if (category.includes('farm') || category.includes('equipment')) {
        return { daily: 1000, weekly: 6000, monthly: 20000 };
      }

      // Default rates
      return { daily: 1200, weekly: 7500, monthly: 25000 };
    };

    const defaultRates = getDefaultRateByCategory();

    // Calculate fallback rates based on daily rate or use category-specific defaults
    const fallbackDaily = dailyRate > 0 ? dailyRate : defaultRates.daily;
    const fallbackWeekly = weeklyRate > 0 ? weeklyRate : (dailyRate > 0 ? dailyRate * 7 * 0.85 : defaultRates.weekly);
    const fallbackMonthly = monthlyRate > 0 ? monthlyRate : (dailyRate > 0 ? dailyRate * 30 * 0.75 : defaultRates.monthly);

    const rates = {
      daily: {
        rate: fallbackDaily,
        label: 'Daily Rate',
        unit: 'per day',
        available: true,
        isFromDB: dailyRate > 0
      },
      weekly: {
        rate: fallbackWeekly,
        label: 'Weekly Rate',
        unit: 'per week',
        available: true,
        isFromDB: weeklyRate > 0
      },
      monthly: {
        rate: fallbackMonthly,
        label: 'Monthly Rate',
        unit: 'per month',
        available: true,
        isFromDB: monthlyRate > 0
      }
    };

    console.log('Calculated rental rates:', rates);
    return rates;
  };

  useEffect(() => {
    const fetchTruckDetails = async () => {
      try {
        setLoading(true);

        // Check if vehicle data exists and has required ID
        if (!vehicle || !vehicle.vehicle_id) {
          console.error('No vehicle data or vehicle_id provided');
          setError('Vehicle information is missing. Please try again.');
          setLoading(false);
          return;
        }

        console.log('Fetching vehicle details for ID:', vehicle.vehicle_id);
        const response = await getVehicleById(vehicle.vehicle_id);
        console.log('Vehicle API response:', response);
        console.log('Vehicle data:', response.vehicle);

        if (response.vehicle) {
          console.log('Rental rates from DB:', {
            daily_rate: response.vehicle.daily_rate,
            weekly_rate: response.vehicle.weekly_rate,
            monthly_rate: response.vehicle.monthly_rate,
            listing_type: response.vehicle.listing_type
          });

          // Enhance vehicle data for hire if rates are missing
          const enhancedVehicle = {
            ...response.vehicle,
            // Ensure we have hire rates even if not in DB
            daily_rate: response.vehicle.daily_rate || getDefaultRateForVehicle(response.vehicle).daily,
            weekly_rate: response.vehicle.weekly_rate || getDefaultRateForVehicle(response.vehicle).weekly,
            monthly_rate: response.vehicle.monthly_rate || getDefaultRateForVehicle(response.vehicle).monthly,
            // Ensure listing type is set for hire
            listing_type: response.vehicle.listing_type || 'hire'
          };

          console.log('Enhanced vehicle data:', enhancedVehicle);
          setTruck(enhancedVehicle);
        } else {
          setTruck(response.vehicle);
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching truck details:', err);
        setError('Failed to load truck details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (vehicle?.vehicle_id) {
      fetchTruckDetails();
    } else {
      console.error('No vehicle or vehicle_id provided to HireDetailsScreen');
      setError('Vehicle information is missing. Please try again.');
      setLoading(false);
    }
  }, [vehicle?.vehicle_id]);

  useEffect(() => {
    if (!loading && !error) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [loading, error]);

  const handleShare = async () => {
    if (!truck) return;

    try {
      const rentalRates = getRentalRates();
      const dailyRate = rentalRates.daily?.rate || 'Contact for pricing';
      const shareUrl = `https://trucksonsale.co.za/vehicle.php?id=${truck.vehicle_id}`;
      const rateText = typeof dailyRate === 'number' ? `R${Math.round(dailyRate).toLocaleString()}/day` : dailyRate;

      const result = await Share.share({
        message: `Check out this ${truck.year} ${truck.make} ${truck.model} available for hire from ${rateText} on Trucks On Sale!\n${shareUrl}`,
        url: shareUrl,
        title: `${truck.year} ${truck.make} ${truck.model} for Hire - Trucks On Sale`
      });
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert(
        'Sharing Error',
        'Unable to share this listing. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const checkAvailability = async (startDate, endDate) => {
    if (!truck) return;

    try {
      setCheckingAvailability(true);
      const availabilityData = await checkVehicleAvailability(truck.vehicle_id, startDate, endDate);
      setAvailability(availabilityData);
      return availabilityData.available;
    } catch (error) {
      console.error('Error checking availability:', error);
      Alert.alert(
        'Availability Check Failed',
        'Unable to check vehicle availability. Please try again.',
        [{ text: 'OK' }]
      );
      return false;
    } finally {
      setCheckingAvailability(false);
    }
  };

  const handleRentalEnquiry = () => {
    setIsEnquiryModalVisible(true);
  };

  const createBooking = async (bookingData) => {
    try {
      const rentalRates = getRentalRates();
      const selectedRate = rentalRates[selectedRentalPeriod];

      const booking = {
        vehicle_id: truck.vehicle_id,
        customer_name: bookingData.customerName,
        email: bookingData.email,
        phone: bookingData.phone,
        start_date: bookingData.startDate,
        end_date: bookingData.endDate,
        pickup_location: `${truck.city}, ${truck.region}`,
        daily_rate: selectedRate?.rate || rentalRates.daily?.rate,
        total_cost: bookingData.totalCost,
        rental_period: selectedRentalPeriod
      };

      const response = await createHireBooking(booking);

      Alert.alert(
        'Booking Submitted',
        'Your hire request has been submitted successfully! We will contact you shortly to confirm the details.',
        [{ text: 'OK' }]
      );

      return response;
    } catch (error) {
      console.error('Error creating booking:', error);
      Alert.alert(
        'Booking Failed',
        'Unable to submit your booking request. Please try again or contact us directly.',
        [{ text: 'OK' }]
      );
      throw error;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF8C00" />
        <Text style={styles.loadingText}>Loading hire details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF8C00" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            setError(null);
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!truck) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#FF8C00" />
        <Text style={styles.errorText}>Vehicle not found</Text>
        <Text style={styles.errorSubText}>
          The vehicle details could not be loaded. Please check your connection and try again.
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            setError(null);
            // Retry fetching
            if (vehicle?.vehicle_id) {
              const fetchTruckDetails = async () => {
                try {
                  const response = await getVehicleById(vehicle.vehicle_id);
                  setTruck(response.vehicle);
                  setError(null);
                } catch (err) {
                  console.error('Error fetching truck details:', err);
                  setError('Failed to load truck details. Please try again later.');
                } finally {
                  setLoading(false);
                }
              };
              fetchTruckDetails();
            }
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Image Carousel */}
      <View style={{ width: '100%', alignItems: 'center', justifyContent: 'center' }}>
        <ImageCarousel
          images={truck.images?.length ? truck.images : (vehicle.images || [])}
          width={width}
          height={width > 768 ? 400 : 300}
          showIndicators={true}
          showCounter={true}
          borderRadius={0}
          style={{}}
        />
        {/* Hire Badge Overlay */}
        <View style={styles.hireBadgeOverlay}>
          <MaterialCommunityIcons name="calendar-clock" size={20} color="#fff" />
          <Text style={styles.hireBadgeText}>Available for Hire</Text>
        </View>
      </View>

      <Animated.View style={[styles.content, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>{`${truck.year} ${truck.make}`}</Text>
            <Text style={styles.model}>{truck.model}</Text>
          </View>
          <TouchableOpacity
            style={styles.shareButton}
            onPress={handleShare}
          >
            <Ionicons name="share-social-outline" size={24} color="#fff" />
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
        </View>

     

        {/* Rental Rates Section */}
        <View style={styles.rentalSection}>
          <Text style={styles.sectionTitle}>Rental Rates</Text>
          <View style={styles.rentalOptions}>
            {Object.entries(getRentalRates()).filter(([, info]) => info.available).map(([period, info]) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.rentalOption,
                  selectedRentalPeriod === period && styles.selectedRentalOption
                ]}
                onPress={() => setSelectedRentalPeriod(period)}
              >
                <Text style={[
                  styles.rentalLabel,
                  selectedRentalPeriod === period && styles.selectedRentalLabel
                ]}>
                  {info.label}
                </Text>
                <Text style={[
                  styles.rentalRate,
                  selectedRentalPeriod === period && styles.selectedRentalRate
                ]}>
                  R{Math.round(info.rate).toLocaleString()}
                </Text>
                <Text style={[
                  styles.rentalUnit,
                  selectedRentalPeriod === period && styles.selectedRentalUnit
                ]}>
                  {info.unit}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Hire Information */}
        <View style={styles.hireInfoSection}>
          <Text style={styles.sectionTitle}>Hire Information</Text>
          <View style={styles.hireInfoGrid}>
            <HireInfoItem
              icon="time-outline"
              title="Minimum Rental"
              value="1 Day"
            />
            <HireInfoItem
              icon="location-outline"
              title="Pickup Location"
              value={`${truck.city}, ${truck.region}`}
            />
{/*     
            <HireInfoItem
              icon="card-outline"
              title="Deposit Required"
              value={`R${Math.round((getRentalRates().daily?.rate || 1200) * 5).toLocaleString()}`}
            /> */}
          </View>
        </View>

        {/* Basic Information */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Year" value={truck.year || 'N/A'} />
            <SpecItem title="Condition" value={truck.condition_type || truck.condition || 'N/A'} />
            <SpecItem title="Mileage" value={truck.mileage ? `${truck.mileage.toLocaleString()} km` : 'N/A'} />
            <SpecItem title="Color" value={truck.color || 'N/A'} />
            <SpecItem title="Region" value={truck.region || 'N/A'} />
            <SpecItem title="City" value={truck.city || 'N/A'} />
          </View>
        </View>

        {/* Engine & Performance */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Engine & Performance</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Engine Type" value={truck.engine_type || 'N/A'} />
            <SpecItem title="Engine Capacity" value={truck.engine_capacity || 'N/A'} />
            <SpecItem title="Horsepower" value={truck.horsepower ? `${truck.horsepower} HP` : 'N/A'} />
            <SpecItem title="Fuel Type" value={truck.fuel_type || 'N/A'} />
            <SpecItem title="Transmission" value={truck.transmission || 'N/A'} />
            {truck.hours_used > 0 ? (
              <SpecItem title="Hours Used" value={`${truck.hours_used.toLocaleString()} hrs`} />
            ) : null}
          </View>
        </View>

        {/* Vehicle Details */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Vehicle Details</Text>
          <View style={styles.specificationGrid}>
            <SpecItem title="Category" value={truck.category || 'N/A'} />
            {truck.subcategory ? (
              <SpecItem title="Subcategory" value={truck.subcategory} />
            ) : null}
            {truck.variant ? (
              <SpecItem title="Variant" value={truck.variant} />
            ) : null}
            <SpecItem title="Condition Rating" value={truck.condition_rating || 'N/A'} />
            {truck.vin_number ? (
              <SpecItem title="VIN Number" value={truck.vin_number} />
            ) : null}
            {truck.registration_number ? (
              <SpecItem title="Registration" value={truck.registration_number} />
            ) : null}
          </View>
        </View>

        {/* Features & Condition */}
        <View style={styles.specificationContainer}>
          <Text style={styles.sectionTitle}>Features & Condition</Text>
          <View style={styles.featuresList}>
            {truck.no_accidents ? (
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>No Accidents</Text>
              </View>
            ) : null}
            {truck.warranty ? (
              <View style={styles.featureItem}>
                <Ionicons name="shield-checkmark" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Warranty Available</Text>
              </View>
            ) : null}
            {truck.service_history ? (
              <View style={styles.featureItem}>
                <Ionicons name="document-text" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Service History Available</Text>
              </View>
            ) : null}
            {truck.roadworthy ? (
              <View style={styles.featureItem}>
                <Ionicons name="car-sport" size={20} color="#4CAF50" />
                <Text style={styles.featureText}>Roadworthy Certificate</Text>
              </View>
            ) : null}
          </View>
        </View>

        {/* Description */}
        {truck.description ? (
          <View style={styles.specificationContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.descriptionText}>{truck.description}</Text>
          </View>
        ) : null}

        {/* Additional Features */}
        {truck.features ? (
          <View style={styles.specificationContainer}>
            <Text style={styles.sectionTitle}>Additional Features</Text>
            <Text style={styles.descriptionText}>{truck.features}</Text>
          </View>
        ) : null}

        {/* Warranty Details */}
        {truck.warranty_details ? (
          <View style={styles.specificationContainer}>
            <Text style={styles.sectionTitle}>Warranty Details</Text>
            <Text style={styles.descriptionText}>{truck.warranty_details}</Text>
          </View>
        ) : null}

        {/* Hire Terms */}
        <View style={styles.termsSection}>
          <Text style={styles.sectionTitle}>Hire Terms & Conditions</Text>
          <View style={styles.termsList}>
            <TermItem text="Valid driver's license required" />
            {/* <TermItem text="Security deposit required before pickup" /> */}
            {/* <TermItem text="Fuel to be returned at same level" />
            <TermItem text="24/7 roadside assistance included" />
            <TermItem text="Comprehensive insurance coverage" /> */}
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.enquiryButton}
            onPress={handleRentalEnquiry}
          >
            <MaterialCommunityIcons name="calendar-plus" size={24} color="#fff" />
            <Text style={styles.enquiryButtonText}>Book Rental</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.callButton}
            onPress={() => {
              Alert.alert(
                'Contact Us',
                'Call us at +27 11 123 4567 for immediate assistance',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Call Now', onPress: () => console.log('Calling...') }
                ]
              );
            }}
          >
            <Ionicons name="call" size={24} color="#FF8C00" />
            <Text style={styles.callButtonText}>Call Now</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      <EnquiryModal
        visible={isEnquiryModalVisible}
        onClose={() => setIsEnquiryModalVisible(false)}
        truckDetails={truck}
        isHireEnquiry={true}
        selectedRentalPeriod={selectedRentalPeriod}
        rentalRates={getRentalRates()}
        onBookingSubmit={createBooking}
        checkAvailability={checkAvailability}
        checkingAvailability={checkingAvailability}
      />
    </ScrollView>
  );
};

// Component for hire information items
const HireInfoItem = ({ icon, title, value }) => (
  <View style={styles.hireInfoItem}>
    <Ionicons name={icon} size={20} color="#FF8C00" />
    <Text style={styles.hireInfoTitle}>{title}</Text>
    <Text style={styles.hireInfoValue}>{value}</Text>
  </View>
);

// Component for specification items
const SpecItem = ({ title, value }) => (
  <View style={styles.specItem}>
    <Text style={styles.specTitle}>{title}</Text>
    <Text style={styles.specValue}>{value}</Text>
  </View>
);

// Component for terms list items
const TermItem = ({ text }) => (
  <View style={styles.termItem}>
    <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
    <Text style={styles.termText}>{text}</Text>
  </View>
);

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 14 : 16;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    fontSize: BASE_FONT_SIZE,
    color: '#333',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  errorSubText: {
    marginTop: 8,
    fontSize: BASE_FONT_SIZE * 0.875,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  retryButton: {
    marginTop: 24,
    backgroundColor: '#FF8C00',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
  },
  hireBadgeOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#FF8C00',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  hireBadgeText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE * 0.875,
    fontWeight: 'bold',
    marginLeft: 6,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  title: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: 'bold',
    color: '#333',
  },
  model: {
    fontSize: BASE_FONT_SIZE * 1.125,
    color: '#FF8C00',
    marginTop: 4,
  },
  shareButton: {
    backgroundColor: '#FF8C00',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  shareButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: BASE_FONT_SIZE * 1.125,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  rentalSection: {
    backgroundColor: '#FFF8F0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#FFE0B3',
  },
  rentalOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  rentalOption: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  selectedRentalOption: {
    borderColor: '#FF8C00',
    backgroundColor: '#FFF8F0',
  },
  rentalLabel: {
    fontSize: BASE_FONT_SIZE * 0.875,
    color: '#666',
    marginBottom: 4,
  },
  selectedRentalLabel: {
    color: '#FF8C00',
    fontWeight: '600',
  },
  rentalRate: {
    fontSize: BASE_FONT_SIZE * 1.25,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  selectedRentalRate: {
    color: '#FF8C00',
  },
  rentalUnit: {
    fontSize: BASE_FONT_SIZE * 0.75,
    color: '#666',
  },
  selectedRentalUnit: {
    color: '#FF8C00',
  },
  hireInfoSection: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  hireInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  hireInfoItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    width: SCREEN_WIDTH > 768 ? '23%' : '47%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  hireInfoTitle: {
    fontSize: BASE_FONT_SIZE * 0.75,
    color: '#666',
    marginTop: 6,
    marginBottom: 2,
    textAlign: 'center',
  },
  hireInfoValue: {
    fontSize: BASE_FONT_SIZE * 0.875,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  specificationContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  specificationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  specItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    width: SCREEN_WIDTH > 768 ? '31%' : '47%',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  specTitle: {
    color: '#FF8C00',
    fontSize: BASE_FONT_SIZE * 0.875,
    marginBottom: 4,
  },
  specValue: {
    color: '#333',
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
  },
  termsSection: {
    backgroundColor: '#f8fff8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e8f5e8',
  },
  termsList: {
    gap: 12,
  },
  termItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  termText: {
    flex: 1,
    fontSize: BASE_FONT_SIZE * 0.875,
    color: '#333',
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  enquiryButton: {
    flex: 2,
    backgroundColor: '#FF8C00',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  enquiryButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
  },
  callButton: {
    flex: 1,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#FF8C00',
    gap: 8,
  },
  callButtonText: {
    color: '#FF8C00',
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
  },
  // Debug styles (for development)
  debugSection: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  debugTitle: {
    fontSize: BASE_FONT_SIZE * 0.875,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: BASE_FONT_SIZE * 0.75,
    color: '#666',
    marginBottom: 2,
  },
  featuresList: {
    marginTop: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 4,
  },
  featureText: {
    marginLeft: 8,
    fontSize: BASE_FONT_SIZE,
    color: '#333',
    flex: 1,
  },
  descriptionText: {
    fontSize: BASE_FONT_SIZE,
    color: '#666',
    lineHeight: BASE_FONT_SIZE * 1.5,
    marginTop: 8,
  },
});

export default HireDetailsScreen;
