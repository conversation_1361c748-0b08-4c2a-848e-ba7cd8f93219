import { Alert, Linking, Platform } from 'react-native';

// Try to import optional dependencies with fallbacks
let AsyncStorage;
let Constants;

try {
  AsyncStorage = require('@react-native-async-storage/async-storage').default;
} catch (e) {
  console.warn('AsyncStorage not available, using fallback');
  AsyncStorage = {
    getItem: () => Promise.resolve(null),
    setItem: () => Promise.resolve(),
    removeItem: () => Promise.resolve(),
  };
}

try {
  Constants = require('expo-constants').default;
} catch (e) {
  console.warn('expo-constants not available, using fallback');
  Constants = {
    expoConfig: { version: '1.0.0' },
    manifest: { version: '1.0.0' }
  };
}

// Version configuration
const API_BASE_URL = 'https://trucksonsale.co.za/api';
const VERSION_CHECK_URL = `${API_BASE_URL}/version/check`;
const LAST_CHECK_KEY = 'last_version_check';
const DISMISSED_VERSION_KEY = 'dismissed_version';
const CHECK_INTERVAL = 12 * 60 * 60 * 1000; // 12 hours in milliseconds

// Get current app version from app.json
export const getCurrentVersion = () => {
  return Constants.expoConfig?.version || Constants.manifest?.version || '1.0.0';
};

// Get current build number/version code
export const getBuildNumber = () => {
  return Constants.expoConfig?.ios?.buildNumber || 
         Constants.expoConfig?.android?.versionCode || 
         '1';
};

// Compare version strings (semantic versioning)
export const compareVersions = (version1, version2) => {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);
  
  // Ensure both arrays have the same length
  const maxLength = Math.max(v1parts.length, v2parts.length);
  while (v1parts.length < maxLength) v1parts.push(0);
  while (v2parts.length < maxLength) v2parts.push(0);
  
  for (let i = 0; i < maxLength; i++) {
    if (v1parts[i] > v2parts[i]) return 1;
    if (v1parts[i] < v2parts[i]) return -1;
  }
  return 0;
};

// Check if enough time has passed since last check
export const shouldCheckForUpdate = async () => {
  try {
    const lastCheck = await AsyncStorage.getItem(LAST_CHECK_KEY);
    if (!lastCheck) return true;
    
    const lastCheckTime = parseInt(lastCheck);
    const now = Date.now();
    
    return (now - lastCheckTime) >= CHECK_INTERVAL;
  } catch (error) {
    console.error('Error checking last update time:', error);
    return true; // Default to checking if there's an error
  }
};

// Save the last check timestamp
export const saveLastCheckTime = async () => {
  try {
    await AsyncStorage.setItem(LAST_CHECK_KEY, Date.now().toString());
  } catch (error) {
    console.error('Error saving last check time:', error);
  }
};

// Check if user has dismissed this version
export const hasUserDismissedVersion = async (version) => {
  try {
    const dismissedVersion = await AsyncStorage.getItem(DISMISSED_VERSION_KEY);
    return dismissedVersion === version;
  } catch (error) {
    console.error('Error checking dismissed version:', error);
    return false;
  }
};

// Save dismissed version
export const saveDismissedVersion = async (version) => {
  try {
    await AsyncStorage.setItem(DISMISSED_VERSION_KEY, version);
  } catch (error) {
    console.error('Error saving dismissed version:', error);
  }
};

// Fetch latest version from server
export const fetchLatestVersion = async () => {
  try {
    const currentVersion = getCurrentVersion();
    const buildNumber = getBuildNumber();
    
    const response = await fetch(VERSION_CHECK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        current_version: currentVersion,
        build_number: buildNumber,
        platform: Platform.OS,
        app_id: Constants.expoConfig?.slug || 'trucks-on-sale'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      latestVersion: data.latest_version,
      currentVersion: currentVersion,
      updateAvailable: data.update_available,
      forceUpdate: data.force_update || false,
      updateUrl: data.update_url,
      releaseNotes: data.release_notes || '',
      downloadUrl: Platform.OS === 'ios' ? data.ios_url : data.android_url
    };
  } catch (error) {
    console.error('Error fetching latest version:', error);
    return {
      success: false,
      error: error.message,
      currentVersion: getCurrentVersion()
    };
  }
};

// Show update dialog
export const showUpdateDialog = (versionInfo, onUpdate, onDismiss) => {
  const { latestVersion, releaseNotes, forceUpdate, downloadUrl } = versionInfo;
  
  const title = forceUpdate ? 'Update Required' : 'Update Available';
  const message = `A new version (${latestVersion}) is available.\n\n${releaseNotes || 'Bug fixes and improvements.'}`;
  
  const buttons = [];
  
  if (!forceUpdate) {
    buttons.push({
      text: 'Later',
      style: 'cancel',
      onPress: () => {
        saveDismissedVersion(latestVersion);
        if (onDismiss) onDismiss();
      }
    });
  }
  
  buttons.push({
    text: forceUpdate ? 'Update Now' : 'Update',
    onPress: () => {
      if (downloadUrl) {
        Linking.openURL(downloadUrl);
      } else {
        // Fallback to app store
        const storeUrl = Platform.OS === 'ios' 
          ? 'https://apps.apple.com/app/trucks-on-sale/id123456789' // Replace with actual App Store ID
          : 'https://play.google.com/store/apps/details?id=com.lesa2022.trucksonsale';
        Linking.openURL(storeUrl);
      }
      if (onUpdate) onUpdate();
    }
  });

  Alert.alert(title, message, buttons, { 
    cancelable: !forceUpdate 
  });
};

// Main function to check for updates
export const checkForUpdates = async (showNoUpdateMessage = false) => {
  try {
    // Check if enough time has passed
    const shouldCheck = await shouldCheckForUpdate();
    if (!shouldCheck && !showNoUpdateMessage) {
      return { checked: false, reason: 'Too soon since last check' };
    }

    // Fetch latest version info
    const versionInfo = await fetchLatestVersion();
    
    // Save the check time
    await saveLastCheckTime();

    if (!versionInfo.success) {
      if (showNoUpdateMessage) {
        Alert.alert('Update Check Failed', 'Unable to check for updates. Please try again later.');
      }
      return { checked: true, success: false, error: versionInfo.error };
    }

    // Check if update is available
    if (versionInfo.updateAvailable) {
      // Check if user has already dismissed this version
      const hasDismissed = await hasUserDismissedVersion(versionInfo.latestVersion);
      
      if (!hasDismissed || versionInfo.forceUpdate) {
        showUpdateDialog(versionInfo);
        return { 
          checked: true, 
          success: true, 
          updateAvailable: true, 
          versionInfo 
        };
      } else {
        return { 
          checked: true, 
          success: true, 
          updateAvailable: true, 
          dismissed: true 
        };
      }
    } else {
      if (showNoUpdateMessage) {
        Alert.alert('Up to Date', 'You are using the latest version of the app.');
      }
      return { 
        checked: true, 
        success: true, 
        updateAvailable: false, 
        versionInfo 
      };
    }
  } catch (error) {
    console.error('Error in checkForUpdates:', error);
    if (showNoUpdateMessage) {
      Alert.alert('Update Check Failed', 'Unable to check for updates. Please try again later.');
    }
    return { checked: true, success: false, error: error.message };
  }
};

// Manual update check (for settings or menu)
export const manualUpdateCheck = () => {
  return checkForUpdates(true);
};

// Reset update check (for testing)
export const resetUpdateCheck = async () => {
  try {
    await AsyncStorage.removeItem(LAST_CHECK_KEY);
    await AsyncStorage.removeItem(DISMISSED_VERSION_KEY);
    console.log('Update check data reset');
  } catch (error) {
    console.error('Error resetting update check:', error);
  }
};

export default {
  getCurrentVersion,
  getBuildNumber,
  compareVersions,
  checkForUpdates,
  manualUpdateCheck,
  resetUpdateCheck,
  shouldCheckForUpdate,
  fetchLatestVersion,
  showUpdateDialog
};
