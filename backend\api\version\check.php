<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get request body
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit();
}

// Extract request data
$currentVersion = $data['current_version'] ?? '1.0.0';
$buildNumber = $data['build_number'] ?? '1';
$platform = $data['platform'] ?? 'unknown';
$appId = $data['app_id'] ?? 'trucks-on-sale';

// Version configuration
$versionConfig = [
    'latest_version' => '1.0.1', // Update this when releasing new versions
    'minimum_version' => '1.0.0', // Minimum supported version
    'release_notes' => 'Bug fixes and performance improvements. Enhanced vehicle search and improved user experience.',
    'ios_url' => 'https://apps.apple.com/app/trucks-on-sale/id123456789', // Replace with actual App Store URL
    'android_url' => 'https://play.google.com/store/apps/details?id=com.lesa2022.trucksonsale',
    'force_update_below' => '1.0.0', // Force update for versions below this
    'update_url' => 'https://trucksonsale.co.za/download' // Generic download page
];

// Function to compare version strings
function compareVersions($version1, $version2) {
    $v1parts = array_map('intval', explode('.', $version1));
    $v2parts = array_map('intval', explode('.', $version2));
    
    // Ensure both arrays have the same length
    $maxLength = max(count($v1parts), count($v2parts));
    while (count($v1parts) < $maxLength) $v1parts[] = 0;
    while (count($v2parts) < $maxLength) $v2parts[] = 0;
    
    for ($i = 0; $i < $maxLength; $i++) {
        if ($v1parts[$i] > $v2parts[$i]) return 1;
        if ($v1parts[$i] < $v2parts[$i]) return -1;
    }
    return 0;
}

// Check if update is available
$updateAvailable = compareVersions($versionConfig['latest_version'], $currentVersion) > 0;

// Check if force update is required
$forceUpdate = compareVersions($currentVersion, $versionConfig['force_update_below']) <= 0;

// Log the version check (optional)
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'current_version' => $currentVersion,
    'build_number' => $buildNumber,
    'platform' => $platform,
    'app_id' => $appId,
    'update_available' => $updateAvailable,
    'force_update' => $forceUpdate,
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
];

// You can log this to a file or database for analytics
// file_put_contents('version_checks.log', json_encode($logData) . "\n", FILE_APPEND);

// Prepare response
$response = [
    'success' => true,
    'current_version' => $currentVersion,
    'latest_version' => $versionConfig['latest_version'],
    'update_available' => $updateAvailable,
    'force_update' => $forceUpdate,
    'release_notes' => $versionConfig['release_notes'],
    'update_url' => $versionConfig['update_url'],
    'ios_url' => $versionConfig['ios_url'],
    'android_url' => $versionConfig['android_url'],
    'minimum_version' => $versionConfig['minimum_version'],
    'platform' => $platform,
    'check_timestamp' => time()
];

// Add platform-specific download URL
if ($platform === 'ios') {
    $response['download_url'] = $versionConfig['ios_url'];
} else if ($platform === 'android') {
    $response['download_url'] = $versionConfig['android_url'];
} else {
    $response['download_url'] = $versionConfig['update_url'];
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);

// Optional: Track version check statistics
try {
    // You can add database logging here if needed
    // Example:
    // $db = new PDO('mysql:host=localhost;dbname=trucks_on_sale', $username, $password);
    // $stmt = $db->prepare("INSERT INTO version_checks (current_version, platform, check_time, ip_address) VALUES (?, ?, NOW(), ?)");
    // $stmt->execute([$currentVersion, $platform, $_SERVER['REMOTE_ADDR']]);
} catch (Exception $e) {
    // Log error but don't fail the response
    error_log("Version check logging error: " . $e->getMessage());
}
?>
